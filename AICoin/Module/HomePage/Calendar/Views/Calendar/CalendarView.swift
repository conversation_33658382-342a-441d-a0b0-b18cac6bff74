//
//  CalendarView.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-18.
//  Copyright © 2024 AICoin. All rights reserved.
//

import JTAppleCalendar
import SnapKit
import UIKit

class CalendarView: UIView {

    // MARK: - 属性

    private var viewModel: CalendarViewModel!

    // 当前视图模式：月视图或周视图
    private var numberOfRows: Int = 6  // 默认为月视图（6行）

    // MARK: - UI组件

    private lazy var weekdayStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .center

        let weekdays = ["日", "一", "二", "三", "四", "五", "六"].map { $0.base.localized }
        for weekday in weekdays {
            let label = UILabel()
            label.text = weekday
            label.textAlignment = .center
            label.font = UIFont.systemFont(ofSize: 12)
            label.textColor = UIColor.themeColor(day: 0x7A8899, night: 0xC3C7D9)
            stackView.addArrangedSubview(label)
        }

        return stackView
    }()

    private lazy var calendarView: JTACMonthView = {
        let calendar = JTACMonthView()
        calendar.backgroundColor = .designKit.secondaryBackgroundNew
        calendar.scrollingMode = .stopAtEachCalendarFrame
        calendar.showsHorizontalScrollIndicator = false
        calendar.allowsMultipleSelection = false
        calendar.scrollDirection = .horizontal
        calendar.minimumLineSpacing = 0
        calendar.minimumInteritemSpacing = 0
        calendar.cellSize = 0
        calendar.calendarDelegate = self
        calendar.calendarDataSource = self
        calendar.register(CalendarDateCell.self, forCellWithReuseIdentifier: "CalendarDateCell")
        return calendar
    }()

    // MARK: - 初始化

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupViews()
    }

    // MARK: - 设置

    private func setupViews() {
        backgroundColor = .designKit.secondaryBackgroundNew

        addSubview(weekdayStackView)
        addSubview(calendarView)

        weekdayStackView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
            make.height.equalTo(24)
        }

        calendarView.snp.makeConstraints { make in
            make.top.equalTo(weekdayStackView.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
    }

    func configure(with viewModel: CalendarViewModel) {
        self.viewModel = viewModel

        // 初始化日历视图
        calendarView.scrollToDate(viewModel.selectedDate, animateScroll: false)
        calendarView.selectDates([viewModel.selectedDate])

        // 添加标记数据更新的监听
        viewModel.onMarksUpdated = { [weak self] in
            guard let self = self else { return }

            // 刷新日历视图以显示最新的标记
            DispatchQueue.main.async {
                self.calendarView.reloadData()
            }
        }
    }

    func scrollToDate(_ date: Date, animated: Bool = true) {
        calendarView.scrollToDate(date, animateScroll: animated)
    }

    func selectDates(_ dates: [Date]) {
        calendarView.selectDates(dates)
    }
}

// MARK: - JTACMonthViewDataSource
extension CalendarView: JTACMonthViewDataSource {

    func configureCalendar(_ calendar: JTACMonthView) -> ConfigurationParameters {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy MM dd"

        let startDate = formatter.date(from: "2020 01 01")!
        let endDate = formatter.date(from: "2030 12 31")!

        return ConfigurationParameters(
            startDate: startDate,
            endDate: endDate,
            numberOfRows: numberOfRows,
            generateInDates: .forAllMonths,
            generateOutDates: .tillEndOfRow,
            firstDayOfWeek: .sunday,
            hasStrictBoundaries: numberOfRows > 1  // 月视图时严格边界，周视图时非严格边界
        )
    }

    // 切换视图模式：月视图/周视图
    func switchViewMode(to mode: CalendarFoldableView.CalendarViewMode) {
        switch mode {
        case .month:
            numberOfRows = 6  // 月视图显示6行
        case .week:
            numberOfRows = 1  // 周视图显示1行
        }

        // 重新加载日历数据
        calendarView.reloadData(withAnchor: calendarView.selectedDates.first ?? Date())
    }
}

// MARK: - JTACMonthViewDelegate
extension CalendarView: JTACMonthViewDelegate {
    func calendar(
        _ calendar: JTACMonthView, cellForItemAt date: Date, cellState: CellState,
        indexPath: IndexPath
    ) -> JTACDayCell {
        let cell =
            calendar.dequeueReusableCell(withReuseIdentifier: "CalendarDateCell", for: indexPath)
            as! CalendarDateCell
        self.calendar(
            calendar, willDisplay: cell, forItemAt: date, cellState: cellState, indexPath: indexPath
        )
        return cell
    }

    func calendar(
        _ calendar: JTACMonthView, willDisplay cell: JTACDayCell, forItemAt date: Date,
        cellState: CellState, indexPath: IndexPath
    ) {
        guard let calendarCell = cell as? CalendarDateCell else { return }

        // 设置日期文本
        calendarCell.dateLabel.text = cellState.text

        // 设置日期颜色
        if cellState.dateBelongsTo == .thisMonth {
            calendarCell.dateLabel.textColor = UIColor.baseTheme.current.cellTitleColor
        } else {
            calendarCell.dateLabel.textColor = UIColor.themeColor(day: 0xD9D9D9, night: 0x667180)
        }

        // 选中状态
        if cellState.isSelected {
            calendarCell.dateLabel.textColor = .white
            calendarCell.selectedView.isHidden = false
            calendarCell.selectedView.backgroundColor = UIColor(hexString: "#1478FA")
        } else {
            calendarCell.selectedView.isHidden = true
        }

        // 当前日期特殊标记
        let today = Calendar.current.isDateInToday(date)
        if today {
            if !cellState.isSelected {
                calendarCell.dateLabel.textColor = UIColor(hexString: "#1478FA")
                calendarCell.todayView.isHidden = false
                calendarCell.todayView.backgroundColor = UIColor(hexString: "#1478FA")
                    .withAlphaComponent(0.1)
            } else {
                calendarCell.todayView.isHidden = true
            }
        } else {
            calendarCell.todayView.isHidden = true
        }

        // 事件标记
        calendarCell.eventIndicator.isHidden = true

        if viewModel.hasEvents(for: date) {
            calendarCell.eventIndicator.isHidden = false

            if viewModel.hasImportantEvents(for: date) {
                // 检查是否是已过期的重要事件
                if viewModel.hasExpiredImportantEvents(for: date) {
                    // 已过期的重要事件显示灰色
                    calendarCell.eventIndicator.backgroundColor = UIColor(hexString: "#D9D9D9")
                } else {
                    // 未过期的重要事件显示红色
                    calendarCell.eventIndicator.backgroundColor = UIColor(hexString: "#E54829")
                }
            } else {
                calendarCell.eventIndicator.backgroundColor = UIColor(hexString: "#D9D9D9")
            }
        }
    }

    func calendar(
        _ calendar: JTACMonthView, didSelectDate date: Date, cell: JTACDayCell?,
        cellState: CellState, indexPath: IndexPath
    ) {
        guard let calendarCell = cell as? CalendarDateCell else { return }

        calendarCell.dateLabel.textColor = .white
        calendarCell.selectedView.isHidden = false
        calendarCell.todayView.isHidden = true

        // 直接通知ViewModel处理日期选择，不再通过委托链
        viewModel.selectDate(date)
    }

    func calendar(
        _ calendar: JTACMonthView, didDeselectDate date: Date, cell: JTACDayCell?,
        cellState: CellState, indexPath: IndexPath
    ) {
        guard let calendarCell = cell as? CalendarDateCell else { return }

        // 恢复非选中状态
        if cellState.dateBelongsTo == .thisMonth {
            calendarCell.dateLabel.textColor = UIColor.baseTheme.current.cellTitleColor
        } else {
            calendarCell.dateLabel.textColor = UIColor(hexString: "#D9D9D9")
        }

        calendarCell.selectedView.isHidden = true

        // 如果是今天，恢复今天的样式
        if Calendar.current.isDateInToday(date) {
            calendarCell.dateLabel.textColor = UIColor(hexString: "#1478FA")
            calendarCell.todayView.isHidden = false
            calendarCell.todayView.backgroundColor = UIColor(hexString: "#1478FA")
                .withAlphaComponent(0.1)
        }
    }

    func calendar(
        _ calendar: JTACMonthView, didScrollToDateSegmentWith visibleDates: DateSegmentInfo
    ) {
        if let date = visibleDates.monthDates.first?.date {
            // 通知视图模型月份已更改
            viewModel.moveToMonth(date: date)
        }
    }

    func calendar(
        _ calendar: JTACMonthView, sizeForItemAt date: Date, position: CellState,
        indexPath: IndexPath
    ) -> CGSize {
        return CGSize(width: 24, height: 24)
    }
}
