# 事件提醒API集成文档

## 概述

本文档描述了日历事件提醒功能的API集成，包括扩展的数据模型和提醒配置字段的处理。

## API接口

### /api/upgrade/calendar/remind/list

#### 请求参数
```json
{
  "remindType": "current" // "current" 或 "history"
}
```

#### 响应结构
```json
{
  "success": true,
  "data": {
    "lastTime": 1234567890,
    "currentCount": 10,
    "historyCount": 5,
    "list": [
      {
        // 基础事件字段
        "id": "event_123",
        "title": "美联储主席鲍威尔讲话",
        "content": "事件详细描述",
        "startDate": 1234567890000,
        "type": "macro",
        "score": "3",
        "isPending": "0",
        "isRemind": "1",
        "relatedEntrances": [],
        
        // 提醒配置字段（仅在remind/list接口中返回）
        "isApp": "1",      // 是否启用APP提醒
        "isPc": "0",       // 是否启用PC提醒
        "isEmail": "1",    // 是否启用邮件提醒
        "time": 3600,      // 提醒提前时间（秒）
        "remarks": "重要会议" // 用户备注
      }
    ]
  }
}
```

## 数据模型扩展

### CalendarEventAPIItem

扩展了以下提醒配置字段：

```swift
/// 是否启用APP提醒
var isApp: String = "0"

/// 是否启用PC提醒
var isPc: String = "0"

/// 是否启用邮件提醒
var isEmail: String = "0"

/// 提醒提前时间（以秒为单位）
var time: NSNumber = 0

/// 用户设置的备注信息
var remarks: String = ""
```

### CalendarEventModel

扩展了对应的业务字段：

```swift
/// 是否启用APP提醒
var isAppReminderEnabled: Bool = false

/// 是否启用PC提醒
var isPcReminderEnabled: Bool = false

/// 是否启用邮件提醒
var isEmailReminderEnabled: Bool = false

/// 提醒提前时间（以秒为单位）
var reminderTime: Int = 0

/// 用户设置的备注信息
var remarks: String = ""
```

#### 计算属性

```swift
/// 是否有任何提醒方式启用
var hasAnyReminderEnabled: Bool

/// 格式化的提醒时间描述
var formattedReminderTime: String
```

### EventReminderModel

扩展了完整的提醒配置信息：

```swift
struct EventReminderModel {
    let title: String
    let reminderTime: String
    let eventId: String
    let isAppEnabled: Bool
    let isPcEnabled: Bool
    let isEmailEnabled: Bool
    let reminderSeconds: Int
    let remarks: String
    let isExpired: Bool
    
    var hasAnyReminderEnabled: Bool
    var reminderTypesDescription: String
}
```

## 数据转换

### fromAPIItem方法

更新了转换逻辑以处理新的提醒配置字段：

```swift
// 转换提醒配置相关字段
model.isAppReminderEnabled = apiItem.isApp == "1"
model.isPcReminderEnabled = apiItem.isPc == "1"
model.isEmailReminderEnabled = apiItem.isEmail == "1"
model.reminderTime = apiItem.time.intValue
model.remarks = apiItem.remarks
```

## ViewModel功能

### CalendarEventReminderViewModel

新增了以下提醒配置相关方法：

```swift
/// 获取指定事件的提醒配置摘要
func getReminderSummary(for eventId: String) -> String?

/// 获取指定事件的备注信息
func getRemarks(for eventId: String) -> String?

/// 检查指定事件是否已过期
func isEventExpired(eventId: String) -> Bool
```

### 提醒时间格式化

更新了formatReminderTime方法以显示完整的提醒配置：

```swift
// 如果有设置提醒时间，显示提醒配置信息
if event.hasAnyReminderEnabled {
    var reminderTypes: [String] = []
    if event.isAppReminderEnabled { reminderTypes.append("APP") }
    if event.isPcReminderEnabled { reminderTypes.append("PC") }
    if event.isEmailReminderEnabled { reminderTypes.append("邮件") }
    
    let typeString = reminderTypes.joined(separator: "、")
    let timeString = event.formattedReminderTime
    
    return "提醒方式：\(typeString)　\(timeString)"
}
```

## 使用示例

### 获取事件提醒配置

```swift
// 获取提醒摘要
if let summary = viewModel.getReminderSummary(for: eventId) {
    print("提醒配置: \(summary)")
}

// 获取备注信息
if let remarks = viewModel.getRemarks(for: eventId) {
    print("备注: \(remarks)")
}

// 检查是否过期
if viewModel.isEventExpired(eventId: eventId) {
    print("事件已过期")
}
```

### 显示提醒配置

```swift
let event = viewModel.event(at: indexPath)
if let event = event {
    // 显示提醒方式
    if event.hasAnyReminderEnabled {
        reminderLabel.text = event.reminderTypesDescription
        timeLabel.text = event.reminderTime
    } else {
        reminderLabel.text = "未设置提醒"
    }
    
    // 显示备注
    if !event.remarks.isEmpty {
        remarksLabel.text = event.remarks
        remarksLabel.isHidden = false
    } else {
        remarksLabel.isHidden = true
    }
    
    // 处理过期状态
    if event.isExpired {
        titleLabel.textColor = .gray
    }
}
```

## 注意事项

1. **API字段差异**: `/api/upgrade/calendar/list`接口不返回提醒配置字段，只有`/api/upgrade/calendar/remind/list`接口才返回完整的提醒配置信息。

2. **字段类型**: API返回的布尔值字段使用字符串格式（"0"/"1"），需要在转换时处理。

3. **向后兼容**: 新增字段都有默认值，确保与现有代码的兼容性。

4. **过期处理**: 过期事件应该有特殊的UI显示处理。

5. **空值处理**: 备注字段可能为空，需要适当的UI隐藏逻辑。
