//
//  CalendarEventReminderAPIModel.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-19.
//  Copyright © 2024 AICoin. All rights reserved.
//

import Foundation

/// 日历事件提醒API响应模型
@objcMembers
class CalendarEventReminderAPIResponse: NSObject {
    var success: Bool = false
    var data: CalendarEventReminderAPIData?
    var error: String = ""
    var errorCode: Int = 0
    
    class func modelCustomPropertyMapper() -> [String: String] {
        return [:]
    }
}

/// 日历事件提醒API数据模型
@objcMembers
class CalendarEventReminderAPIData: NSObject {
    var lastTime: Int = 0           // 提醒时间戳
    var currentCount: Int = 0       // 未发生事件总数
    var historyCount: Int = 0       // 历史事件总数
    var list: [CalendarEventAPIItem] = []  // 事件列表，复用已有的CalendarEventAPIItem
    
    class func modelContainerPropertyGenericClass() -> [String: Any] {
        return ["list": CalendarEventAPIItem.self]
    }
}

/// 日历事件提醒请求模型
struct CalendarEventReminderRequestModel {
    var remindType: String
    
    init(remindType: ReminderType) {
        switch remindType {
        case .eventReminder:
            self.remindType = "current"
        case .historyReminder:
            self.remindType = "history"
        }
    }
    
    /// 提醒类型枚举
    enum ReminderType {
        case eventReminder   // 当前事件提醒
        case historyReminder // 历史事件提醒
    }
}
